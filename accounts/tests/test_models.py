from django.test import TestCase, tag
from django.contrib.auth.models import User
from accounts.models import UserProfile
from device_manager.models import Device
from fields.models import Field
from notification_center.models import NotificationSettings
from unittest.mock import patch


class BaseUserProfileModelTest(TestCase):
    """Base class for user profile model tests with common setup"""
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            is_superuser=False,
            is_staff=False
        )
        self.profile = UserProfile.objects.create(
            user=self.user,
            role="User",
            phon="********",  # Fixed to 8 digits
            titl="Test Title",
            orgn="Test Organization"
        )
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user
        )


@tag('integration')
class UserProfileModelIntegrationTest(BaseUserProfileModelTest):
    """Tests for user profile model without using mocks"""

    def test_profile_creation(self):
        """Test that a user profile can be created with the expected attributes"""
        self.assertEqual(self.profile.user.username, "testuser")
        self.assertEqual(self.profile.role, "User")
        self.assertEqual(self.profile.phon, "********")
        self.assertEqual(self.profile.titl, "Test Title")
        self.assertEqual(self.profile.orgn, "Test Organization")

    def test_notification_settings_creation(self):
        """Test that notification settings are created correctly"""
        self.assertEqual(self.notification_settings.user, self.user)
        # Add assertions for default notification settings values

    def test_str_representation(self):
        """Test the string representation of a UserProfile object"""
        expected_str = f"{self.user.first_name} {self.user.last_name}'s Profile"
        self.assertEqual(str(self.profile), expected_str)

    def test_phone_validation(self):
        """Test phone number validation"""
        # Test valid phone number
        valid_profile = UserProfile.objects.create(
            user=User.objects.create_user(username="phonetest", password="pass"),
            role="User",
            phon="87654321",
            titl="Phone Test",
            orgn="Test Org"
        )
        self.assertEqual(valid_profile.phon, "87654321")

    def test_device_relationship(self):
        """Test many-to-many relationship with devices"""
        # Create test field first
        field = Field.objects.create(
            name="Test Field",
            cord=[],
            colr="#FF0000",
            covr=0.0,
            loca="Test Location"
        )

        # Create test devices with required fields
        device1 = Device.objects.create(
            name="Test Device 1",
            euid="********90ABCDEF",
            type="Whiskers Node V1",
            fild=field
        )
        device2 = Device.objects.create(
            name="Test Device 2",
            euid="FEDCBA0987654321",
            type="Whiskers Node V1",
            fild=field
        )

        self.profile.devs.add(device1, device2)
        self.assertEqual(self.profile.devs.count(), 2)
        self.assertIn(device1, self.profile.devs.all())
        self.assertIn(device2, self.profile.devs.all())

    def test_role_choices(self):
        """Test that valid role choices work"""
        valid_roles = ["Admin", "Owner", "User"]

        for role in valid_roles:
            user = User.objects.create_user(username=f"role_{role.lower()}", password="pass")
            profile = UserProfile.objects.create(
                user=user,
                role=role,
                phon="********",
                titl="Role Test",
                orgn="Test Org"
            )
            self.assertEqual(profile.role, role)

    def test_optional_fields(self):
        """Test that optional fields can be None or blank"""
        user = User.objects.create_user(username="optional", password="pass")
        profile = UserProfile.objects.create(
            user=user,
            role="User",
            phon="********",
            titl="Optional Test",
            orgn="Test Org",
            dprt=None,  # Optional field
            pict=None   # Optional field
        )
        self.assertIsNone(profile.dprt)
        # For ImageField, check if it's falsy (empty)
        self.assertFalse(profile.pict)


@tag('unit')
class UserProfileModelUnitTest(TestCase):
    """Unit tests for user profile model using mocks to isolate functionality"""

    def setUp(self):
        # Create a real user for testing
        self.user = User.objects.create_user(
            username="unittest",
            password="unittest",
            first_name="Unit",
            last_name="Test",
            email="<EMAIL>",
            is_superuser=True,
            is_staff=True
        )

    @patch('accounts.models.UserProfile.save')
    def test_profile_save_method_called(self, mock_save):
        """Test that the save method is called when creating a profile"""
        profile = UserProfile(
            user=self.user,
            role="Admin",
            phon="********",
            titl="Unit Test",
            orgn="Unit Organization"
        )
        profile.save()
        mock_save.assert_called_once()

    def test_model_field_max_lengths(self):
        """Test model field max lengths"""
        profile = UserProfile(user=self.user)

        # Test max_length constraints
        self.assertEqual(profile._meta.get_field('role').max_length, 255)
        self.assertEqual(profile._meta.get_field('phon').max_length, 8)
        self.assertEqual(profile._meta.get_field('titl').max_length, 255)
        self.assertEqual(profile._meta.get_field('orgn').max_length, 255)
        self.assertEqual(profile._meta.get_field('dprt').max_length, 255)

    def test_model_field_properties(self):
        """Test model field properties"""
        profile = UserProfile(user=self.user)

        # Test field properties
        user_field = profile._meta.get_field('user')
        self.assertTrue(user_field.one_to_one)

        pict_field = profile._meta.get_field('pict')
        self.assertTrue(pict_field.null)
        self.assertTrue(pict_field.blank)

        dprt_field = profile._meta.get_field('dprt')
        self.assertTrue(dprt_field.null)
        self.assertTrue(dprt_field.blank)

    def test_phone_field_validators(self):
        """Test phone field has correct validators"""
        phon_field = UserProfile._meta.get_field('phon')
        validators = phon_field.validators

        # Should have at least one validator (RegexValidator)
        self.assertTrue(len(validators) > 0)

        # Check if it's a RegexValidator
        from django.core.validators import RegexValidator
        regex_validators = [v for v in validators if isinstance(v, RegexValidator)]
        self.assertTrue(len(regex_validators) > 0)

    def test_model_meta_properties(self):
        """Test model meta properties"""
        meta = UserProfile._meta
        self.assertEqual(meta.verbose_name, "user profile")  # Django auto-generates lowercase
        self.assertEqual(meta.verbose_name_plural, "user profiles")  # Django auto-generates lowercase

    def test_user_profile_cascade_deletion(self):
        """Test that profile is deleted when user is deleted"""
        user = User.objects.create_user(username="deletetest", password="pass")
        profile = UserProfile.objects.create(
            user=user,
            role="User",
            phon="********",
            titl="Delete Test",
            orgn="Test Org"
        )

        profile_id = profile.id
        user.delete()

        # Profile should be deleted due to CASCADE
        self.assertFalse(UserProfile.objects.filter(id=profile_id).exists())

    def test_user_profile_with_department(self):
        """Test user profile with department field"""
        user = User.objects.create_user(username="depttest", password="pass")
        profile = UserProfile.objects.create(
            user=user,
            role="User",
            phon="********",
            titl="Dept Test",
            orgn="Test Org",
            dprt="Engineering"
        )

        self.assertEqual(profile.dprt, "Engineering")

    def test_user_profile_help_texts(self):
        """Test that model fields have correct help texts"""
        user_field = UserProfile._meta.get_field('user')
        self.assertEqual(user_field.help_text, "User profile owner.")

        role_field = UserProfile._meta.get_field('role')
        self.assertEqual(role_field.help_text, "User role.")

        phon_field = UserProfile._meta.get_field('phon')
        self.assertEqual(phon_field.help_text, "User phone number.")

        titl_field = UserProfile._meta.get_field('titl')
        self.assertEqual(titl_field.help_text, "Title")

        orgn_field = UserProfile._meta.get_field('orgn')
        self.assertEqual(orgn_field.help_text, "Organization")

        dprt_field = UserProfile._meta.get_field('dprt')
        self.assertEqual(dprt_field.help_text, "Department")

        devs_field = UserProfile._meta.get_field('devs')
        self.assertEqual(devs_field.help_text, "Devices the user has access to.")