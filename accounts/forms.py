from django import forms
from django.core.validators import EmailValidator, ValidationError
import phonenumbers
from device_manager.models import Device
from fields.models import Field
from notification_center.forms import DeviceSelectionWidget


class UserAndProfileForm(forms.Form):
    pict = forms.ImageField(
        required=False,
        label="Profile Picture",
        widget=forms.FileInput(attrs={"class": "form-control", "type": "file"}),
    )
    fnam = forms.CharField(
        label="First Name",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    lnam = forms.CharField(
        label="Last Name",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    usrn = forms.CharField(
        label="Username",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    emal = forms.EmailField(
        label="Email",
        widget=forms.EmailInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    role = forms.ChoiceField(
        label="Role",
        choices=[
            ("Admin", "Admin"),
            ("Owner", "Owner"),
            ("User", "User"),
        ],
        widget=forms.Select(
            attrs={
                "class": "form-select col-sm-2",
            }
        ),
    )
    phon = forms.CharField(
        label="Phone Number",
        required=False,
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "8",
            }
        ),
    )
    pwrd = forms.CharField(
        label="Password",
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    titl = forms.CharField(
        label="Title",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    orgn = forms.CharField(
        label="Organization",
        widget=forms.TextInput(
            attrs={
                "class": "form-control",
                "maxlength": "254",
            }
        ),
    )
    devs = forms.ModelMultipleChoiceField(
        required=False,
        queryset=Device.objects.all(),
        label="Devices",
        widget=forms.SelectMultiple(
            attrs={
                "class": "select2 form-control select2-multiple",
                "data-toggle": "select2",
                "multiple": "multiple",
                "data-placeholder": "Choose Devices...",
            }
        ),
    )

    def __init__(self, *args, **kwargs):
        is_create = kwargs.pop("is_create", False)
        user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)
        self.is_create = is_create

        # Use improved device selection widget
        self.fields['devs'].widget = DeviceSelectionWidget(user=user, show_all_devices=True)

    def clean_emal(self):
        emal = self.cleaned_data.get("emal")
        # Add your email validation logic here
        validator = EmailValidator()
        try:
            validator(emal)
        except ValidationError:
            raise forms.ValidationError("Invalid email address")
        return emal

    def clean_usrn(self):
        usrn = self.cleaned_data.get("usrn")
        # Check if username already exists for new users
        if self.is_create and usrn:
            from django.contrib.auth.models import User
            if User.objects.filter(username=usrn).exists():
                raise forms.ValidationError("Username already exists.")
        return usrn

    # def clean_phon(self):
    #     phon = self.cleaned_data.get("phon")
    #     if phon is None:
    #         return None
    #     full_phon = "+968" + phon
    #     try:
    #         parsed_phon = phonenumbers.parse(full_phon, None)
    #         if not phonenumbers.is_valid_number(parsed_phon):
    #             raise forms.ValidationError("Invalid phone number")
    #     except phonenumbers.phonenumberutil.NumberParseException:
    #         raise forms.ValidationError("Invalid phone number")
    #     return phon
