from django.test import TestCase, Client, tag, TransactionTestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from notification_center.models import Event, Notification, NotificationSettings
from notification_center.utils import process_device_messages, send_notifications, deduplicate_events
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile
from unittest.mock import patch, MagicMock
import json
from datetime import timedelta
import threading
import time


@tag('integration')
class NotificationIntegrationTests(TestCase):
    """Integration tests for notification center components"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Create test profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='User',
            phon='********',
            titl='Test Title',
            orgn='Test Organization'
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0********9",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Associate device with user profile
        self.profile.devs.add(self.device)

        # Create notification settings
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )
        self.notification_settings.devs.add(self.device)

    def test_event_creation_to_notification_flow(self):
        """Test the flow from event creation to notification display"""
        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Manually create a notification (normally done by signal)
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Check that the notification appears in the list view
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification, response.context['list'])

        # Mark the notification as read
        notification.read = True
        notification.save()

        # Check that it appears in the read notifications list
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'read'}))
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification, response.context['list'])

        # Check that it doesn't appear in the unread notifications list
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'unread'}))
        self.assertEqual(response.status_code, 200)
        self.assertNotIn(notification, response.context['list'])

    @patch('notification_center.models.send_user_notification')
    @patch('notification_center.models.async_to_sync')
    def test_process_device_messages(self, mock_async_to_sync, mock_send_notification):
        """Test processing device messages and sending notifications"""
        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create a notification but don't mark it as sent
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Process device messages
        process_device_messages(self.device)

        # Check that send_user_notification was called
        mock_send_notification.assert_called()

        # Refresh the notification from the database
        notification.refresh_from_db()

        # Check that the notification was marked as sent
        self.assertTrue(notification.sent)

    @patch('notification_center.models.async_to_sync')
    def test_notification_settings_update(self, mock_async_to_sync):
        """Test updating notification settings and its effect on notifications"""
        # Update notification settings to disable warning notifications
        self.notification_settings.rxwr = False
        self.notification_settings.save()

        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Manually create a notification (normally done by signal)
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Process device messages with mocked send_notifications
        with patch('notification_center.utils.send_notifications') as mock_send:
            process_device_messages(self.device)

            # Check that send_notifications was called
            mock_send.assert_called_once()

    def test_delete_notification(self):
        """Test deleting a notification"""
        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Manually create a notification
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Delete the notification
        response = self.client.get(reverse('notification_center:delete', kwargs={'notification_id': notification.id}))
        self.assertEqual(response.status_code, 302)  # Redirects after successful deletion

        # Check that the notification no longer exists
        with self.assertRaises(Notification.DoesNotExist):
            Notification.objects.get(id=notification.id)

    def test_multiple_events_same_device(self):
        """Test handling multiple events from the same device"""
        # Create multiple events for the same device
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Low battery"
        )

        event2 = Event.objects.create(
            devi=self.device,
            type="Danger",
            desc="Device moved"
        )

        event3 = Event.objects.create(
            devi=self.device,
            type="Info",
            desc="Status update"
        )

        # Create notifications for each event
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event1,
            read=False,
            sent=False
        )

        notification2 = Notification.objects.create(
            user=self.user,
            evnt=event2,
            read=False,
            sent=False
        )

        notification3 = Notification.objects.create(
            user=self.user,
            evnt=event3,
            read=False,
            sent=False
        )

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Check that all notifications appear in the list view
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 200)
        # We only check that our notifications are in the list, not the exact count
        # as there might be other notifications from previous tests
        self.assertIn(notification1, response.context['list'])
        self.assertIn(notification2, response.context['list'])
        self.assertIn(notification3, response.context['list'])

        # Mark one notification as read
        notification1.read = True
        notification1.save()

        # Check that it appears in the read notifications list
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'read'}))
        self.assertEqual(response.status_code, 200)
        # We only check that our notification is in the list, not the exact count
        self.assertIn(notification1, response.context['list'])

        # Check that the other notifications appear in the unread list
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'unread'}))
        self.assertEqual(response.status_code, 200)
        # We only check that our notifications are in the list, not the exact count
        self.assertIn(notification2, response.context['list'])
        self.assertIn(notification3, response.context['list'])

    def test_duplicate_event_handling(self):
        """Test handling duplicate events"""
        # Create an event
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Low battery"
        )

        # Create a duplicate event (same device, type, and description)
        event2 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Low battery"
        )

        # Create notifications for each event
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event1,
            read=False,
            sent=False
        )

        notification2 = Notification.objects.create(
            user=self.user,
            evnt=event2,
            read=False,
            sent=False
        )

        # Process device messages with mocked send_notifications
        with patch('notification_center.utils.send_notifications') as mock_send:
            with patch('notification_center.models.async_to_sync'):
                # Call the function that should deduplicate events
                from notification_center.utils import deduplicate_events
                unique_events, duplicate_events = deduplicate_events([event1, event2])

                # Check that one event is identified as unique and one as duplicate
                self.assertEqual(len(unique_events), 1)
                self.assertEqual(len(duplicate_events), 1)
                self.assertEqual(unique_events[0], event1)
                self.assertEqual(duplicate_events[0], event2)

    def test_multiple_users_same_device(self):
        """Test notifications for multiple users for the same device"""
        # Create another user
        user2 = User.objects.create_user(
            username='testuser2',
            password='testpassword'
        )

        # Create profile for the second user
        profile2 = UserProfile.objects.create(
            user=user2,
            role='User',
            phon='87654321',
            titl='Test Title 2',
            orgn='Test Organization 2'
        )

        # Associate device with both user profiles
        profile2.devs.add(self.device)

        # Create notification settings for the second user
        notification_settings2 = NotificationSettings.objects.create(
            user=user2,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )
        notification_settings2.devs.add(self.device)

        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Manually create notifications for both users
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        notification2 = Notification.objects.create(
            user=user2,
            evnt=event,
            read=False,
            sent=False
        )

        # Log in as first user
        self.client.login(username='testuser', password='testpassword')

        # Check that the first user sees their notification
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification1, response.context['list'])
        self.assertNotIn(notification2, response.context['list'])

        # Log in as second user
        self.client.logout()
        self.client.login(username='testuser2', password='testpassword')

        # Check that the second user sees their notification
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification2, response.context['list'])
        self.assertNotIn(notification1, response.context['list'])
