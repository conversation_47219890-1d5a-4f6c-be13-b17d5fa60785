from django.test import TestCase, Client, tag, RequestFactory
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.messages.storage.fallback import FallbackStorage
from notification_center.models import Notification, NotificationSettings, Event
from notification_center.views import NotificationView, DeliveryOptionsView, notification_delete, read, export_to_csv
from accounts.models import UserProfile
from device_manager.models import Device
from fields.models import Field
from unittest.mock import patch, MagicMock
import json


@tag('unit')
class NotificationViewUnitTests(TestCase):
    """Unit tests for NotificationView with mocks"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Mock Notification objects
        self.notification_mock = MagicMock(spec=Notification)

        # Set up request factory
        self.factory = RequestFactory()

    def test_notification_view_login_required(self):
        """Test that NotificationView requires login"""
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 302)  # Redirects to login page

    def test_notification_view_get_queryset(self):
        """Test the get_queryset method of NotificationView"""
        # Create a notification for the test user
        field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=field
        )

        event = Event.objects.create(
            devi=device,
            type="Warning",
            desc="Test warning event"
        )

        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:list'))

        # Check that the response contains the notification
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification, response.context['list'])

    def test_notification_view_get_queryset_read(self):
        """Test the get_queryset method of NotificationView with read status"""
        # Create notifications with different read statuses
        field = Field.objects.create(
            name="Test Field 2",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#00FF00",
            covr=10.0,
            loca="Test Location 2"
        )

        device = Device.objects.create(
            name="Test Device 2",
            desc="Test device description 2",
            euid="9876543210FEDCBA",
            type="Whiskers Node V1",
            fild=field
        )

        event = Event.objects.create(
            devi=device,
            type="Warning",
            desc="Test warning event 2"
        )

        notification_read = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=True,
            sent=False
        )

        notification_unread = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view with read status
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'read'}))

        # Check that the response contains only the read notification
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification_read, response.context['list'])
        self.assertNotIn(notification_unread, response.context['list'])


@tag('unit')
class DeliveryOptionsViewUnitTests(TestCase):
    """Unit tests for DeliveryOptionsView with mocks"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Create test profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='User',
            phon='12345678',
            titl='Test Title',
            orgn='Test Organization'
        )

        # Set up request factory
        self.factory = RequestFactory()

    def test_delivery_options_view_login_required(self):
        """Test that DeliveryOptionsView requires login"""
        response = self.client.get(reverse('notification_center:delivery_options', kwargs={'profile_id': self.profile.id}))
        self.assertEqual(response.status_code, 302)  # Redirects to login page

    @patch('notification_center.views.NotificationSettings.objects.get_or_create')
    def test_delivery_options_view_get(self, mock_get_or_create):
        """Test the GET method of DeliveryOptionsView"""
        # Set up mock
        mock_settings = MagicMock(spec=NotificationSettings)
        mock_get_or_create.return_value = (mock_settings, True)

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:delivery_options', kwargs={'profile_id': self.profile.id}))
        self.assertEqual(response.status_code, 200)
        mock_get_or_create.assert_called_once_with(user=self.user)


@tag('unit')
class NotificationDeleteUnitTests(TestCase):
    """Unit tests for notification_delete view with mocks"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Set up request factory
        self.factory = RequestFactory()

    @patch('notification_center.views.get_object_or_404')
    def test_notification_delete(self, mock_get_object):
        """Test the notification_delete view"""
        # Set up mock
        mock_notification = MagicMock(spec=Notification)
        mock_notification.user = self.user
        mock_get_object.return_value = mock_notification

        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:delete', kwargs={'notification_id': 1}))
        self.assertEqual(response.status_code, 302)  # Redirects after successful deletion
        mock_notification.delete.assert_called_once()


@tag('integration')
class NotificationViewIntegrationTests(TestCase):
    """Integration tests for NotificationView with real database objects"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create real Notification objects
        self.notification_read = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=True,
            sent=False
        )

        self.notification_unread = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=False,
            sent=False
        )

    def test_notification_view_list(self):
        """Test the notification list view"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['list']), 2)

    def test_notification_view_list_read(self):
        """Test the notification list view filtered by read status"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'read'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['list']), 1)
        self.assertEqual(response.context['list'][0], self.notification_read)

    def test_notification_view_list_unread(self):
        """Test the notification list view filtered by unread status"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:list_status', kwargs={'notification_status': 'unread'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['list']), 1)
        self.assertEqual(response.context['list'][0], self.notification_unread)


@tag('integration')
class DeliveryOptionsViewIntegrationTests(TestCase):
    """Integration tests for DeliveryOptionsView with real database objects"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Create test profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='User',
            phon='12345678',
            titl='Test Title',
            orgn='Test Organization'
        )

        # Create notification settings
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )

    def test_delivery_options_view_get(self):
        """Test the GET method of DeliveryOptionsView"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        response = self.client.get(reverse('notification_center:delivery_options', kwargs={'profile_id': self.profile.id}))
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(response.context['form'])
        self.assertEqual(response.context['form'].instance, self.notification_settings)

    def test_delivery_options_view_post(self):
        """Test the POST method of DeliveryOptionsView"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test the view
        data = {
            'rxif': False,
            'rxup': True,
            'rxwr': True,
            'rxdg': True,
            'mthd': 'SMS'
        }
        response = self.client.post(reverse('notification_center:delivery_options', kwargs={'profile_id': self.profile.id}), data)
        self.assertEqual(response.status_code, 302)  # Redirects after successful form submission

        # Check that the settings were updated
        updated_settings = NotificationSettings.objects.get(user=self.user)
        self.assertEqual(updated_settings.rxif, False)
        self.assertEqual(updated_settings.mthd, 'SMS')


@tag('integration')
class NotificationViewAdditionalIntegrationTests(TestCase):
    """Additional integration tests for notification views"""

    def setUp(self):
        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )

        # Create test profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='User',
            phon='12345678',
            titl='Test Title',
            orgn='Test Organization'
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create real Notification objects
        self.notification_read = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=True,
            sent=False
        )

        self.notification_unread = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=False,
            sent=False
        )

    def test_read_notification_view(self):
        """Test the read notification view"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test marking a specific notification as read
        response = self.client.get(reverse('notification_center:read', kwargs={'notification_id': self.notification_unread.id}))
        self.assertEqual(response.status_code, 200)

        # Check that the notification was marked as read
        self.notification_unread.refresh_from_db()
        self.assertTrue(self.notification_unread.read)

    def test_export_to_csv_view(self):
        """Test the export to CSV view"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Test exporting all notifications
        response = self.client.get(reverse('notification_center:export_csv') + '?notification_status=all')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment; filename="notification_export.csv"', response['Content-Disposition'])

        # Test exporting read notifications
        response = self.client.get(reverse('notification_center:export_csv') + '?notification_status=read')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

        # Test exporting unread notifications
        response = self.client.get(reverse('notification_center:export_csv') + '?notification_status=unread')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

    def test_notification_delete_view(self):
        """Test the notification delete view"""
        # Log in
        self.client.login(username='testuser', password='testpassword')

        # Get the notification ID
        notification_id = self.notification_unread.id

        # Delete the notification
        response = self.client.get(reverse('notification_center:delete', kwargs={'notification_id': notification_id}))
        self.assertEqual(response.status_code, 302)  # Redirects after successful deletion

        # Check that the notification no longer exists
        with self.assertRaises(Notification.DoesNotExist):
            Notification.objects.get(id=notification_id)
