from django.test import TestCase, tag
from django.utils import timezone
from unittest.mock import patch, MagicMock
import json

from device_manager.models import Device, get_device_attrs_template, get_location_template
from fields.models import Field
from device_manager.utils.device_cache import DeviceCache


class BaseDeviceModelTestCase(TestCase):
    """Base test case with common setup for device model tests"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create test devices with different configurations
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Create a second device for testing multiple devices
        self.device2 = Device.objects.create(
            name="Test Device 2",
            desc="Second Test Device",
            euid="FEDCBA9876543210",
            stat="Offline",
            temp=30,
            batt=60,
            chrg=True,
            actv=False,
            mntc=True,
            hidn=True,
            attr={"client": {"temp": 30, "Motion event.": True, "Shock event.": False, "Motionless event.": False}},
            type="Whiskers Node V2",
            aset="Solar",
            loca={"lati": 1.0, "long": 1.0, "alti": 100.0, "oofi": True, "plac": "Indoor"},
            fild=self.field,
            offp=20
        )

        self.device_cache = DeviceCache()


@tag('unit')
class DeviceModelUnitTestCase(BaseDeviceModelTestCase):
    """Unit tests for the Device model with mocked dependencies"""

    def test_device_creation_basic_attributes(self):
        """Test that a device can be created with basic attributes"""
        self.assertEqual(self.device.name, "Test Device")
        self.assertEqual(self.device.desc, "Test Description")
        self.assertEqual(self.device.euid, "ABCDEF0123456789")
        self.assertEqual(self.device.stat, "Online")
        self.assertEqual(self.device.temp, 25)
        self.assertEqual(self.device.batt, 80)
        self.assertEqual(self.device.chrg, False)
        self.assertEqual(self.device.actv, True)
        self.assertEqual(self.device.mntc, False)
        self.assertEqual(self.device.hidn, False)

    def test_device_creation_complex_attributes(self):
        """Test device creation with complex attributes"""
        expected_attr = {"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}}
        self.assertEqual(self.device.attr, expected_attr)
        self.assertEqual(self.device.type, "Whiskers Node V1")
        self.assertEqual(self.device.aset, "Battery")
        expected_loca = {"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"}
        self.assertEqual(self.device.loca, expected_loca)
        self.assertEqual(self.device.fild, self.field)
        self.assertEqual(self.device.offp, 10)

    def test_device_creation_different_configurations(self):
        """Test that devices can be created with different configurations"""
        self.assertEqual(self.device2.name, "Test Device 2")
        self.assertEqual(self.device2.stat, "Offline")
        self.assertEqual(self.device2.temp, 30)
        self.assertEqual(self.device2.batt, 60)
        self.assertTrue(self.device2.chrg)
        self.assertFalse(self.device2.actv)
        self.assertTrue(self.device2.mntc)
        self.assertTrue(self.device2.hidn)

    def test_str_method(self):
        """Test the __str__ method of the Device model"""
        self.assertEqual(str(self.device), "Test Device")
        self.assertEqual(str(self.device2), "Test Device 2")

    def test_to_dict_method_basic_fields(self):
        """Test the to_dict method returns correct basic fields"""
        device_dict = self.device.to_dict()
        self.assertEqual(device_dict["name"], "Test Device")
        self.assertEqual(device_dict["desc"], "Test Description")
        self.assertEqual(device_dict["euid"], "ABCDEF0123456789")
        self.assertEqual(device_dict["stat"], "Online")
        self.assertEqual(device_dict["temp"], 25)
        self.assertEqual(device_dict["batt"], 80)

    def test_to_dict_method_complex_fields(self):
        """Test the to_dict method returns correct complex fields"""
        device_dict = self.device.to_dict()
        self.assertEqual(device_dict["chrg"], False)
        self.assertEqual(device_dict["actv"], True)
        self.assertEqual(device_dict["mntc"], False)
        self.assertEqual(device_dict["hidn"], False)
        expected_attr = {"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}}
        self.assertEqual(device_dict["attr"], expected_attr)
        self.assertEqual(device_dict["type"], "Whiskers Node V1")
        self.assertEqual(device_dict["aset"], "Battery")
        expected_loca = {"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"}
        self.assertEqual(device_dict["loca"], expected_loca)
        self.assertEqual(device_dict["fild"], self.field.to_json())
        self.assertEqual(device_dict["offp"], 10)

    def test_to_json_method(self):
        """Test the to_json method produces valid JSON"""
        device_json = self.device.to_json()
        device_dict = json.loads(device_json)
        self.assertEqual(device_dict["name"], "Test Device")
        self.assertEqual(device_dict["desc"], "Test Description")
        self.assertEqual(device_dict["euid"], "ABCDEF0123456789")

    def test_from_json_method(self):
        """Test the from_json method recreates device correctly"""
        device_json = self.device.to_json()
        new_device = Device.from_json(device_json)
        self.assertEqual(new_device.name, "Test Device")
        self.assertEqual(new_device.desc, "Test Description")
        self.assertEqual(new_device.euid, "ABCDEF0123456789")
        self.assertEqual(new_device.stat, "Online")
        self.assertEqual(new_device.temp, 25)

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_lupd_str_property_with_recent_update(self, mock_get_cached_device):
        """Test the lupd_str property with recent update"""
        mock_get_cached_device.return_value = None

        # Set a recent last update time
        self.device.lupd = timezone.now() - timezone.timedelta(minutes=5)
        self.device.save()

        # Should show time since last update
        self.assertNotEqual(self.device.lupd_str, "—")
        self.assertIn("minute", self.device.lupd_str.lower())

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_lupd_str_property_no_update(self, mock_get_cached_device):
        """Test the lupd_str property when no update time is set"""
        mock_get_cached_device.return_value = None

        # Clear the last update time
        self.device.lupd = None

        # Should show "—" when no update time
        self.assertEqual(self.device.lupd_str, "—")

    @patch('device_manager.utils.device_cache.DeviceCache.cache_device')
    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_save_method_with_caching_new_device(self, mock_get_cached_device, mock_cache_device):
        """Test the save method with caching for new device"""
        # Mock no cached device exists
        mock_get_cached_device.return_value = None

        # Update the device
        self.device.name = "Updated Device"
        self.device.save()

        # Verify that cache_device was called
        mock_cache_device.assert_called_once_with(self.device)

    @patch('device_manager.utils.device_cache.DeviceCache.cache_device')
    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_save_method_with_caching_existing_device(self, mock_get_cached_device, mock_cache_device):
        """Test the save method with caching for existing cached device"""
        # Create a mock cached device
        mock_cached_device = MagicMock(spec=Device)
        for field in Device.critical_fields:
            setattr(mock_cached_device, field, getattr(self.device, field))
        mock_cached_device.pk = self.device.id
        mock_cached_device.id = self.device.id

        mock_get_cached_device.return_value = mock_cached_device

        # Update the device
        self.device.name = "Updated Device"
        self.device.save()

        # Verify that cache_device was called
        mock_cache_device.assert_called_once_with(self.device)
        mock_get_cached_device.assert_called_once_with(self.device.pk)

    def test_device_model_field_validation(self):
        """Test device model field validation and constraints"""
        # Test that required fields are properly set
        self.assertIsNotNone(self.device.name)
        self.assertIsNotNone(self.device.euid)
        self.assertIsNotNone(self.device.type)

        # Test field types
        self.assertIsInstance(self.device.temp, int)
        self.assertIsInstance(self.device.batt, int)
        self.assertIsInstance(self.device.chrg, bool)
        self.assertIsInstance(self.device.actv, bool)
        self.assertIsInstance(self.device.mntc, bool)
        self.assertIsInstance(self.device.hidn, bool)

    def test_device_model_json_serialization_roundtrip(self):
        """Test that device can be serialized to JSON and back without data loss"""
        original_dict = self.device.to_dict()
        json_str = self.device.to_json()
        recreated_device = Device.from_json(json_str)
        recreated_dict = recreated_device.to_dict()

        # Compare key fields (excluding auto-generated fields like timestamps)
        key_fields = ['name', 'desc', 'euid', 'stat', 'temp', 'batt', 'chrg', 'actv', 'mntc', 'hidn', 'type', 'aset']
        for field in key_fields:
            self.assertEqual(original_dict[field], recreated_dict[field], f"Field {field} differs after JSON roundtrip")


@tag('unit')
class DeviceUtilityFunctionsUnitTestCase(TestCase):
    """Unit tests for device utility functions"""

    def test_get_device_attrs_template_valid_type(self):
        """Test get_device_attrs_template with valid device type"""
        attrs = get_device_attrs_template("Whiskers Node V1")
        self.assertIsInstance(attrs, dict)
        self.assertIn("client", attrs)

    def test_get_device_attrs_template_invalid_type(self):
        """Test get_device_attrs_template with invalid device type"""
        attrs = get_device_attrs_template("Invalid Type")
        self.assertEqual(attrs, {})

    def test_get_device_attrs_template_empty_type(self):
        """Test get_device_attrs_template with empty type"""
        attrs = get_device_attrs_template("")
        self.assertEqual(attrs, {})

    def test_get_device_attrs_template_none_type(self):
        """Test get_device_attrs_template with None type"""
        attrs = get_device_attrs_template(None)
        self.assertEqual(attrs, {})

    def test_get_location_template(self):
        """Test get_location_template returns correct default values"""
        location = get_location_template()
        self.assertEqual(location["lati"], 0)
        self.assertEqual(location["long"], 0)
        self.assertEqual(location["alti"], 0)
        self.assertEqual(location["oofi"], False)
        self.assertEqual(location["plac"], "Outdoor")

    def test_get_location_template_immutability(self):
        """Test that get_location_template returns a new dict each time"""
        location1 = get_location_template()
        location2 = get_location_template()

        # Modify one and ensure the other is not affected
        location1["lati"] = 100
        self.assertEqual(location2["lati"], 0)


@tag('integration')
class DeviceModelIntegrationTestCase(BaseDeviceModelTestCase):
    """Integration tests for the Device model with real cache interactions"""

    def setUp(self):
        """Set up test data with cache integration"""
        super().setUp()

        # Try to connect to Redis for integration tests
        try:
            from django_redis import get_redis_connection
            self.redis_client = get_redis_connection("default")
            self.redis_client.ping()
            self.redis_available = True
        except:
            # Skip Redis tests if not available
            self.redis_available = False
            self.redis_client = MagicMock()
            self.redis_client.get.return_value = None
            self.redis_client.set.return_value = True
            self.redis_client.delete.return_value = True

    def tearDown(self):
        """Clean up after tests"""
        if self.redis_available and hasattr(self, 'device'):
            try:
                self.redis_client.delete(f"device:{self.device.id}")
                self.redis_client.delete(f"device:{self.device.id}:stat")
                if hasattr(self, 'device2'):
                    self.redis_client.delete(f"device:{self.device2.id}")
                    self.redis_client.delete(f"device:{self.device2.id}:stat")
            except:
                pass

    def test_save_method_cache_integration(self):
        """Test the save method integrates properly with cache"""
        # Update the device
        original_name = self.device.name
        self.device.name = "Updated Device Integration"
        self.device.save()

        # Verify the device was updated in the database
        updated_device = Device.objects.get(pk=self.device.id)
        self.assertEqual(updated_device.name, "Updated Device Integration")

        # If Redis is available, verify cache was updated
        if self.redis_available:
            cached_json = self.redis_client.get(f"device:{self.device.id}")
            if cached_json:
                cached_data = json.loads(cached_json)
                self.assertEqual(cached_data["name"], "Updated Device Integration")

    def test_lupd_str_property_integration(self):
        """Test the lupd_str property with real time calculations"""
        # Set a specific last update time
        past_time = timezone.now() - timezone.timedelta(minutes=5)
        self.device.lupd = past_time
        self.device.save()

        # The lupd_str should reflect the time difference
        lupd_str = self.device.lupd_str
        self.assertNotEqual(lupd_str, "—")
        self.assertIn("minute", lupd_str.lower())

    def test_device_cache_consistency(self):
        """Test that device cache remains consistent with database"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Cache the device
        self.device_cache.cache_device(self.device)

        # Retrieve from cache
        cached_device = self.device_cache.get_cached_device(self.device.id)

        if cached_device:
            # Verify cached device matches database device
            self.assertEqual(cached_device.name, self.device.name)
            self.assertEqual(cached_device.euid, self.device.euid)
            self.assertEqual(cached_device.stat, self.device.stat)

    def test_multiple_device_cache_operations(self):
        """Test cache operations with multiple devices"""
        if not self.redis_available:
            self.skipTest("Redis not available for integration test")

        # Cache both devices
        self.device_cache.cache_device(self.device)
        self.device_cache.cache_device(self.device2)

        # Retrieve both from cache
        cached_device1 = self.device_cache.get_cached_device(self.device.id)
        cached_device2 = self.device_cache.get_cached_device(self.device2.id)

        # Verify both devices are correctly cached
        if cached_device1:
            self.assertEqual(cached_device1.name, self.device.name)
        if cached_device2:
            self.assertEqual(cached_device2.name, self.device2.name)
