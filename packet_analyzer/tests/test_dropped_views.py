from django.test import TestCase, Client, tag
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth.models import User
from datetime import timedelta
import json

from packet_analyzer.models import DroppedPacket
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile


class DroppedViewTestBase(TestCase):
    """Base class for dropped view tests"""

    def setUp(self):
        # Create test users
        self.regular_user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )

        self.superuser = User.objects.create_superuser(
            username='admin',
            password='adminpassword',
            email='<EMAIL>'
        )

        # Create test field
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 23.5, "lng": 58.4}]),
            colr="#FF5733",
            covr=10.5,
            loca="Test Location",
            work_shifts={}
        )

        # Create test devices
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Device Description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        self.gateway = Device.objects.create(
            name="Test Gateway",
            desc="Test Gateway Description",
            euid="9876543210ABCDEF",
            type="Whiskers Gateway V1",
            fild=self.field
        )

        # Create user profile and assign devices
        self.user_profile = UserProfile.objects.create(
            user=self.regular_user,
            role="User",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.user_profile.devs.add(self.device, self.gateway)

        self.admin_profile = UserProfile.objects.create(
            user=self.superuser,
            role="Admin",
            titl="Admin Title",
            orgn="Admin Organization"
        )

        # Create test client
        self.client = Client()

        # Create dropped packets
        self.now = timezone.now()
        for i in range(15):  # Create 15 packets to test pagination
            DroppedPacket.objects.create(
                devi=self.device if i % 2 == 0 else None,
                gate=self.gateway if i % 3 == 0 else None,
                data={"error": f"test_error_{i}"},
                expt=f"Test exception {i}",
                rxat=self.now - timedelta(minutes=30 - i)
            )


@tag('integration')
class DroppedListViewIntegrationTest(DroppedViewTestBase):
    """Integration tests for DroppedListView"""

    def test_dropped_list_view_requires_login(self):
        """Test that the dropped list view requires login"""
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 302)  # Redirects to login page
        self.assertIn('login', response.url)

    def test_dropped_list_view_with_incorrect_credentials(self):
        """Test that the dropped list view rejects incorrect credentials"""
        # Try to login with incorrect password
        login_success = self.client.login(username='admin', password='wrongpassword')
        self.assertFalse(login_success)

        # Try to access the view after failed login
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 302)  # Still redirects to login page
        self.assertIn('login', response.url)

    def test_dropped_list_view_with_nonexistent_user(self):
        """Test that the dropped list view rejects nonexistent users"""
        # Try to login with a nonexistent user
        login_success = self.client.login(username='nonexistentadmin', password='password')
        self.assertFalse(login_success)

        # Try to access the view after failed login
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 302)  # Still redirects to login page
        self.assertIn('login', response.url)

    def test_dropped_list_view_requires_superuser(self):
        """Test that the dropped list view requires superuser"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_dropped_list_view_with_superuser(self):
        """Test that the dropped list view works with superuser"""
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'packet_analyzer/dropped_list.html')

    def test_dropped_list_view_context(self):
        """Test that the dropped list view provides the correct context"""
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))

        # Check that the context contains the packet list
        self.assertIn('packet_list', response.context)

        # Check that the context contains the paginator
        self.assertIn('paginator', response.context)

        # Check that the paginator has the correct number of pages
        # With 15 packets and 10 per page, we should have 2 pages
        self.assertEqual(response.context['paginator'].num_pages, 2)

    def test_dropped_list_view_pagination(self):
        """Test that the dropped list view paginates correctly"""
        self.client.login(username='admin', password='adminpassword')

        # Test first page
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(len(response.context['packet_list']), 10)

        # Test second page
        response = self.client.get(reverse('packet_analyzer:dropped_list') + '?page=2')
        self.assertEqual(len(response.context['packet_list']), 5)

    def test_dropped_list_view_ordering(self):
        """Test that the dropped list view orders the queryset correctly"""
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))

        # Check that the queryset is ordered by rxat in descending order
        packets = list(response.context['packet_list'])
        for i in range(len(packets) - 1):
            self.assertGreaterEqual(packets[i].rxat, packets[i + 1].rxat)


@tag('e2e')
class DroppedListViewE2ETest(DroppedViewTestBase):
    """End-to-end tests for DroppedListView"""

    def test_dropped_list_view_with_real_superuser(self):
        """Test that the dropped list view works with real superuser"""
        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the response contains the expected data
        for i in range(min(10, len(DroppedPacket.objects.all()))):
            self.assertContains(response, f"Test exception {14-i}")

    def test_dropped_list_view_with_invalid_page(self):
        """Test that the dropped list view handles invalid page numbers gracefully"""
        self.client.login(username='admin', password='adminpassword')

        # Test with a page number that's too high
        response = self.client.get(reverse('packet_analyzer:dropped_list') + '?page=999')
        self.assertEqual(response.status_code, 404)

        # Test with a non-numeric page number
        response = self.client.get(reverse('packet_analyzer:dropped_list') + '?page=abc')
        self.assertEqual(response.status_code, 404)

    def test_dropped_list_view_with_empty_database(self):
        """Test that the dropped list view handles an empty database gracefully"""
        # Delete all dropped packets
        DroppedPacket.objects.all().delete()

        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the context contains an empty packet list
        self.assertEqual(len(response.context['packet_list']), 0)

    def test_dropped_list_view_with_staff_user(self):
        """Test that the dropped list view rejects staff users who are not superusers"""
        # Create a staff user who is not a superuser
        staff_user = User.objects.create_user(
            username='staffuser',
            password='staffpassword',
            email='<EMAIL>',
            is_staff=True,
            is_superuser=False
        )

        # Create user profile for staff user
        UserProfile.objects.create(
            user=staff_user,
            role="User",
            titl="Staff User",
            orgn="Test Organization"
        )

        # Login as staff user
        self.client.login(username='staffuser', password='staffpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))

        # Staff user who is not a superuser should be forbidden
        self.assertEqual(response.status_code, 403)

    def test_dropped_list_view_with_null_device_packets(self):
        """Test that the dropped list view handles packets with null device references"""
        # Create a packet with null device and gateway
        DroppedPacket.objects.create(
            devi=None,
            gate=None,
            data={"error": "null_device_error"},
            expt="Test exception with null device",
            rxat=self.now
        )

        self.client.login(username='admin', password='adminpassword')
        response = self.client.get(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 200)

        # Check that the response contains the null device packet
        self.assertContains(response, "Test exception with null device")

    def test_dropped_list_view_with_http_methods(self):
        """Test that the dropped list view handles different HTTP methods appropriately"""
        self.client.login(username='admin', password='adminpassword')

        # Test with POST method (should not be allowed)
        response = self.client.post(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

        # Test with PUT method (should not be allowed)
        response = self.client.put(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

        # Test with DELETE method (should not be allowed)
        response = self.client.delete(reverse('packet_analyzer:dropped_list'))
        self.assertEqual(response.status_code, 405)  # Method Not Allowed
